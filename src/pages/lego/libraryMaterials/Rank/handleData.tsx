import { ColumnsType, Progress, Tooltip } from '@blmcp/ui';
import { ResData, SortType } from '../../components/types';
import { DimensionInfo } from '../../api/types';
import isMobile from '../../utils/isMobile';
import { formatValue, getText } from '../module/utils';
import { calculateColumnWidth } from '../../utils/css';
import no1 from './image/no1.png';
import no2 from './image/no2.png';
import no3 from './image/no3.png';

// 查找对应tips字段
const findFieldAfterMerge = (dimension, measure, keyValue) => {
  const mergedArray = [...dimension, ...measure];
  const foundItem = mergedArray.find((item) => item?.key === keyValue);
  console.log(mergedArray, 'mergedArray---', foundItem, keyValue);
  return foundItem?.tips;
};

export const handleData =
  (dimensionInfo: DimensionInfo[]) => (data: ResData) => {
    let dataSource = data?.values ?? [];
    const dimension = (data?.dimensionInfo || []).map((item, index) => ({
      ...item,
      dataIndex: item.key + '_D' + index,
    }));
    const measure = (data?.measureInfo || []).map((item, index) => ({
      ...item,
      dataIndex: item.key + '_M' + index,
    }));
    const sort = data?.sort?.[0] ?? {};
    // 选出数值最大，做进度条用
    const maxKey = measure?.[0]?.key + '_M0';
    const targetList: any[] = dataSource?.map((item) => item[maxKey]) || [];
    const maxValue = targetList?.length
      ? Math.max.apply(null, targetList)
      : null;
    // 找出按照哪列排序，自己做排名列
    let sortIndex = [...dimension, ...measure].find(
      (item: any) =>
        String(item.sortType) === '1' || String(item.sortType) === '2',
    )?.dataIndex;
    if (!sortIndex) {
      // 如果没有设置排序，默认按照数值列
      sortIndex = measure?.[0]?.dataIndex;
    }
    if (sortIndex) {
      let rank = 1;
      dataSource = dataSource.map((item: any, index) => {
        if (index > 0) {
          if (
            dataSource[index - 1][sortIndex] !== dataSource[index][sortIndex]
          ) {
            rank += 1;
          }
        }
        return {
          ...item,
          rank: rank,
        };
      });
    }

    const columns: ColumnsType<unknown> = (
      dimensionInfo?.map((dim: any, index) => {
        // 寻找指标
        const dataIndex = dim.dataIndex;
        return {
          title: isMobile() ? (
            <Tooltip
              title={getText(dim.title, dim)}
              overlayStyle={{ zIndex: 5 }}
            >
              {getText(dim.title, dim)}
            </Tooltip>
          ) : (
            <Tooltip
              title={findFieldAfterMerge(
                data.dimensionInfo,
                data.measureInfo,
                dim.key,
              )}
              overlayStyle={{ zIndex: 5 }}
            >
              {getText(dim.title, dim)}
            </Tooltip>
          ),
          dataIndex,
          index,
          key: dataIndex,
          defaultSortOrder:
            index === sort.index - 1
              ? String(sort.sortOrder) === SortType.ASC
                ? 'ascend'
                : 'descend'
              : undefined,
          width: calculateColumnWidth(getText(dim.title, dim), 120),
          id: dim.columnId,
          fieldType: dim.fieldType,
          ellipsis: true,
          render(record: any) {
            let formatInfo = {};
            if (dim.type === 'dimension') {
              formatInfo = dimension[dim.typeIndex] || {};
            } else if (dim.type === 'measure') {
              formatInfo = measure[dim.typeIndex] || {};
            }
            const text = formatValue(record, formatInfo);
            return isMobile() ? (
              <Tooltip title={text} overlayStyle={{ zIndex: 5 }}>
                {text}
              </Tooltip>
            ) : (
              text
            );
          },
        };
      }) ?? []
    ).map((item, index) => {
      if (index === 0) {
        return {
          ...item,
          width: calculateColumnWidth(item?.title, 230),
          render: (text, record: any) => {
            if (item.dataIndex) {
              return (
                <div className="dimension-value-wrapper">
                  <Tooltip title={text} overlayStyle={{ zIndex: 5 }}>
                    <div
                      className="dimension-value"
                      style={{
                        display: 'inline-block',
                        width: '40%',
                        verticalAlign: 'middle',
                      }}
                    >
                      {text}
                    </div>
                  </Tooltip>
                  <div
                    style={{
                      display: 'inline-block',
                      width: '60%',
                      paddingRight: '5px',
                      overflow: 'hidden',
                      verticalAlign: 'middle',
                    }}
                  >
                    <Progress
                      strokeColor="rgb(22, 119, 255)"
                      percent={
                        // 如果都是负数，则都不显示进度条了（和产品健民确认过）
                        maxValue > 0 && record[maxKey]
                          ? (Number(record[maxKey]) / maxValue) * 100
                          : 0
                      }
                      showInfo={false}
                    />
                  </div>
                </div>
              );
            } else {
              return null;
            }
          },
        };
      }
      return item;
    });
    return {
      columns: [
        {
          title: isMobile() ? (
            <Tooltip overlayStyle={{ zIndex: 5 }} title="排名">
              排名
            </Tooltip>
          ) : (
            '排名'
          ),
          key: 'rank',
          width: 60,
          dataIndex: 'rank',
          render(text: any) {
            const map: any = { 1: no1, 2: no2, 3: no3 };
            if (map[text]) {
              return (
                <span style={{ position: 'relative', top: '2px' }}>
                  <img src={map[text]} width="22"></img>
                </span>
              );
            }
            return (
              <span style={{ paddingLeft: text < 10 ? '6px' : '3px' }}>
                {text}
              </span>
            );
          },
        },
        ...columns,
      ], // 列信息描述
      dataSource, // 数据信息
      total: data?.totalSize,
      pageNum: data?.pageNo,
      pageSize: data?.pageSize,
    };
  };
