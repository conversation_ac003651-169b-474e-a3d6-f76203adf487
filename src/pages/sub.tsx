import LegoRender from '@blm/bi-lego-sdk/LegoRender';
import { report } from '@blm/bi-lego-sdk/utils';
import React from 'react';

window.report = report;

const filterProps = {
  adcode: {
    // 默认值
    defaultValue: ['北京市'],
    clearable: true,
  },
  senior_manager_name: {
    defaultValue: ['杜乐乐'],
  },
  car_team_name: {
    defaultValue: ['长安汽车'],
    options: [
      {
        label: '长安汽车',
        value: '长安汽车',
      },
      {
        label: '比亚迪',
        value: '比亚迪',
      },
    ],
    // defaultValue: ['长安汽车'],
    // clearable: true,
  },
  tenant_name: {
    defaultValue: ['约约出行'],
  },
  dur_online: {
    defaultValue: [0, 100],
  },
};
export default () => {
  const [show, setShow] = React.useState(false);
  return (
    <>
      <button onClick={() => setShow(!show)}>切换</button>
      {show && (
        <LegoRender
          filterProps={filterProps}
          reportId="1780498439921533691"
        ></LegoRender>
      )}
      <LegoRender
        // filterProps={filterProps}
        reportKey="operationalDataMonitoring"
      ></LegoRender>
      <LegoRender
        // filterProps={filterProps}
        reportKey="financeBusinessAnalysis"
      ></LegoRender>
    </>
  );
};
