import { useState, useCallback } from 'react';
import { message } from '@blmcp/ui';
import { getComponentData, getCardData } from '@/pages/lego/api';
import { FunType } from '@/utils/store';
import { ComponentProps } from '@/pages/lego/type';
import { globalCache } from '@/pages/lego/utils/cache';
import showTips from '@/pages/lego/utils/showTips';
// import oneMessage from '@/utils/oneMessage';
import useComponent, { ComponentValue, store } from './useComponent';

// 通过数据集、组件类型、筛选条件转换为key
function transFromDataSetToKey(
  componentId: string,
  dataSetConfig: ComponentProps<any>['dataSetConfig'],
  params: any,
): string[] {
  // 数据集key
  // const dataSetConfig = dataSetConfig || {};
  return [
    componentId,
    JSON.stringify(dataSetConfig || {}),
    JSON.stringify(params || {}),
  ];
}

export default function useComponentData<T>({
  componentId,
  publishStatus,
  isEdit,
  uuid,
  reportId,
}: {
  componentId: string;
  publishStatus: 1 | 0;
  isEdit: boolean;
  uuid: string;
  reportId: string;
}): [
  T | undefined,
  (
    params: unknown,
    others?: unknown,
    dataSetConfig?: ComponentProps<any>['dataSetConfig'],
  ) => void,
  [ComponentValue, FunType<ComponentValue>],
] {
  const [meta, setMeta] = useComponent(componentId);
  // 图表数据
  const [data, setData] = useState<any>();

  const queryData = useCallback(
    async (
      params: any,
      others: any = {},
      dataSetConfig: ComponentProps<any>['dataSetConfig'],
    ) => {
      const sourceMeta = store.get(componentId);
      const query = {
        reportId,
        elementId: sourceMeta.elementId,
        componentType: sourceMeta.componentType,
        requestDataType: sourceMeta.dataType,
        ...params,
        ...others,
        publishStatus,
        // dailySecond: 2,
      };

      if (query.elementId === undefined || sourceMeta.isLinkDataSet === false)
        return;

      /**
       * 接口缓存复用
       * 编辑态生效，查看页面不使用, 点击搜索不会触发缓存
       * 对比规则：
       *    数据集id 相等
       *    拖拽的数据集（全量相等）
       *    同一个组件
       * 缓存有效期： 1h
       */

      // 转为key前先判断 数据id、组件类型是否相等
      let cacheKey: string[] = [];
      if (isEdit) {
        cacheKey = transFromDataSetToKey(componentId, dataSetConfig, query);
        const cacheData = globalCache.get(cacheKey);
        if (cacheData && isEdit) {
          setMeta({ loading: false }, true);
          setData(cacheData);
          return;
        } else {
          // 缓存不是新的话需要删除掉
          globalCache.deleteByIndexId(cacheKey[0]);
        }
      } else {
        // 在预览态可根据组件id查询到参数， 缓存逻辑影响不到；
        globalCache.setRelationship(
          transFromDataSetToKey(componentId, dataSetConfig, query),
        );
      }

      setMeta({ loading: true }, true);
      try {
        // 判断是否是指标卡
        const res =
          sourceMeta.dataType !== 31
            ? await getComponentData(query, sourceMeta.repeatedSubmission)
            : await getCardData(query, sourceMeta.repeatedSubmission);
        setData(res.data);

        setMeta({ loading: false, error: null }, true);
        // 设置缓存
        if (isEdit) {
          // 缓存需要删除掉之前的
          globalCache.add(cacheKey, res.data);
        }

        // 数据粒度不支持筛选器提示
        // if (res.data?.showFTP) {
        //   oneMessage.success('部分图表的数据集中缺少字段，筛选条件未生效');
        // }
      } catch (e: any) {
        if (e.code === 401 || e.code === 173403) {
          setMeta({ error: e }, true);
          // 无数据集权限显示alert[增量]
          showTips.add(e.msg, uuid);
        } else if (e.msg) {
          message.error(e.msg);
        }

        setData(undefined);
        // 对于重复请求、取消请求不做处理， 因为有新的请求不能取消loading
        if (!['请勿重复请求', '取消请求'].includes(e)) {
          setMeta({ loading: false }, true);
        }
        // 失败删除之前缓存
        if (isEdit) {
          globalCache.delete(cacheKey);
        }
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    },
    [],
  );

  return [data, queryData, [meta, setMeta]];
}
