import { ResData } from '../../components/types';
import { formatValue, getText } from '../module/utils';

/**
 *  饼图数据的处理
 * @param data 返回的数据
 * @returns
 */
/**
 *
 * _M{数字} 指标 有这个截取之前的中文 没有正常匹配title
 * _D{数字} 维度 有这个截取之前的中文 没有正常匹配title
 * _C{数字} 对比列 有这个截取之前的中文 没有正常匹配title
 */
const getSuffix = (
  title: string,
  key: string,
  index: number,
  firstData: any,
  type: string,
): string => {
  const titleField = `${title}_${type}${index}`;
  const keyField = `${key}_${type}${index}`;
  if (firstData[titleField] !== undefined) return titleField;
  if (firstData[keyField] !== undefined) return keyField;
  return '';
};

// 定义我们的饼图数据格式
interface PieChartData {
  data: Array<{
    name: string;
    value: number;
    valueText: string;
    originalValue: number;
  }>;
  text: string;
  total: string;
}

export const transformPieData = (data: ResData): PieChartData => {
  const { key, title } = data?.dimensionInfo?.[0] ?? {};
  const measureInfo = data?.measureInfo?.[0] ?? {};

  const { title: IndexText, key: indexK } = measureInfo;
  const firstData = data?.values?.[0] ?? {};

  const dimKey = getSuffix(title, key, 0, firstData, 'D');
  const indexKey = getSuffix(IndexText, indexK, 0, firstData, 'M');
  const indexPercentKey = getSuffix(IndexText, indexK, 1, firstData, 'M');

  // 计算总值
  const total =
    data?.values?.reduce((sum, item) => {
      const value = item[indexKey] as number;
      return sum + (isNaN(value) ? 0 : value);
    }, 0) || 0;

  // 为ECharts格式准备数据
  const chartData =
    data?.values?.map((item) => {
      const value = item[indexKey] as number;
      let percentageText = '';
      if (indexPercentKey) {
        const percentage = (Number(item[indexPercentKey]) * 100).toFixed(2);
        percentageText = `(${percentage}%)`;
      }

      return {
        name: item[dimKey] as string,
        value: value,
        valueText: `${formatValue(
          value,
          data?.measureInfo?.[0] ?? {},
        )}${percentageText}`, // 在valueText中包含百分比
        originalValue: value, // 保留原始值
      };
    }) || [];

  // 返回我们的ECharts组件期望的格式
  return {
    data: chartData,
    text: getText(IndexText, measureInfo),
    total: measureInfo?.advanceComputeModeId
      ? ''
      : `{label|总计}\n{value|${String(total) || 0}}`,
  };
};
