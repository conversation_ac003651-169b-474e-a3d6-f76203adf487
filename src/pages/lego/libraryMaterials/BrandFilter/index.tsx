import { BLMTenantList } from '@blmcp/peento-businessComponents';
import { useMemo } from 'react';
import FilterWrapper from '../wrapper/FilterWrapper';
import DispatchWrapper from '../wrapper/DDispatchWrapper';
import stateContext from '../../context/filterContext';

interface NewTenantFilterProps {
  __id?: string; // 预览模式
  componentId?: string; // 编辑模式
  uuid: string; // 唯一标识
}

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  // 不参与获取数据集相关事情
  notQueryData: true,
};

export const BrandFilter = function (props: NewTenantFilterProps) {
  const BusinessType = Number(sessionStorage.currentBusinessGroup || '1');
  return (
    <DispatchWrapper context={stateContext(props.uuid)} store="tenant">
      <FilterWrapper
        defaultValue={1}
        componentProps={props}
        fieldProps={useMemo(() => {
          return {
            key: 'tenant_id',
            columnId: 100000013,
            dataType: 1,
          };
        }, [])}
      >
        <BLMTenantList
          style={{ maxWidth: '100%' }}
          platformUsageList={[1, 2]}
          isBusinessGroup={true}
          isShowTestTenantsOnline={true}
          businessGroup={[BusinessType]}
          businessType={[BusinessType]}
          platformStatusEnum={[1, 2]}
          clearable={false}
        />
      </FilterWrapper>
    </DispatchWrapper>
  );
};
