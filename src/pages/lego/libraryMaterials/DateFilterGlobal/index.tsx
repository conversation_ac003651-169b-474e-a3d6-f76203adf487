/**
 * 日期组件
 */
import { useEffect, useState, useRef } from 'react';
import dayjs from 'dayjs';
import relationCenterExp from '@/pages/lego/libraryMaterials/module/RelationCenter';
import { DatePickerFilter as DatePickerFilterView } from '../../components/Filter/DatePickerFilter';
import queryCenterExp from '../module/Query';
import linkageCenterExp from '../module/Linkage';
import { getPresets } from '../../components/Filter/DatePickerFilter/tools';
import { DEFAULT_DATE_RANGE } from '../DatePickerFilter/constants/dateRangeOptions';

interface DatePickerFilterProps {
  __id: string;
  componentId: string;
  defaultValue: any;
  disabledType: boolean;
  uuid: string;
  dateRange?: number;
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          label: '维度',
          key: 'dimensionInfo',
          hideLabelSuffix: true,
          ignoreSetDefaultComputeType: true,
          placeholder: '默认时间维度',
        },
      ],
      indexDisabled: true,
      dimDisabled: true,
      dataSetDisabled: true,
    },
  },
  // 默认时间
  defaultValue: {
    componentName: 'DatePickerDefaultValue',
    // 该组件其他配置项
    props: {},
  },

  // 是否禁用时间组件
  disabledType: {
    componentName: 'DatePickerDisabled',
    // 该组件其他配置项
    props: {},
  },

  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {
      noPadding: true,
    },
  },
  // 跨度可选范围
  dateRange: {
    componentName: 'DatePickerRange',
    // 该组件其他配置项
    props: {},
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图`表 4:筛选器 5:文本 41:默认筛选器
  componentType: 410,
  // 不参与获取数据集相关事情
  notQueryData: true,
};

const temp_dateMap: any = {};
setTimeout(() => {
  const { project } = window.AliLowCodeEngine || {};
  // 暂时通过监听历史变化，重置缓存，后面统一处理默认值问题
  project?.currentDocument?.history.onChangeCursor(() => {
    // eslint-disable-next-line guard-for-in
    for (let key in temp_dateMap) {
      delete temp_dateMap[key];
    }
  });
});

// 逻辑层
export const DateFilterGlobal = ({
  __id,
  componentId,
  defaultValue,
  disabledType,
  dateRange = DEFAULT_DATE_RANGE,
  uuid,
}: DatePickerFilterProps) => {
  const linkageCenter = linkageCenterExp(uuid);
  const queryCenter = queryCenterExp(uuid);
  const relationCenter = relationCenterExp(uuid);
  const defaultValueRef = useRef(defaultValue);
  const filterId = __id ?? componentId ?? '';
  const presets = getPresets();
  // 重置强制刷新用
  const [key, setKey] = useState(() => Date.now());
  // 不设置默认值，就按照近七天处理
  const defaultDateItem =
    presets.find((item) => item.type === defaultValue?.dateType) ||
    presets.find((item) => item.type === 'last-7');

  useEffect(() => {
    if (
      JSON.stringify(defaultValueRef.current) !== JSON.stringify(defaultValue)
    ) {
      defaultValueRef.current = defaultValue;
      temp_dateMap[filterId] = null;
      setKey(Date.now());
    }
  }, [defaultValue]);

  // 监听时间跨度变化，手动重置为当前默认值
  useEffect(() => {
    temp_dateMap[filterId] = {
      value: defaultDateItem?.value,
      type: temp_dateMap[filterId]?.type,
    };
    setKey(Date.now());
  }, [dateRange]);

  // 初始化
  useEffect(() => {
    const defaultValue = temp_dateMap[filterId]?.value || defaultDateItem.value;
    relationCenter.registerFilter(`date_global_${filterId}`);
    queryCenter.setQuery(filterId, {
      columnId: 100000010,
      key: 't_date',
      dataType: 2,
      dateFilterRelativeUnit:
        temp_dateMap[filterId]?.type || defaultDateItem.timeType, // 默认是自定义粒度
      fieldValue: [
        dayjs(defaultValue[0]).startOf('day').valueOf(),
        dayjs(defaultValue[1]).endOf('day').valueOf(),
      ],
      fieldLabel: [
        dayjs(defaultValue[0]).startOf('day').format('YYYY-MM-DD') +
          ' ~ ' +
          dayjs(defaultValue[1]).endOf('day').format('YYYY-MM-DD'),
      ],
    });
    setTimeout(() => {
      relationCenter.readyFilter(`date_global_${filterId}`);
    }, 0);
  }, [filterId, defaultValue]);

  useEffect(() => {
    // 重置操作
    const resetFilter = () => {
      setKey(Date.now());
      temp_dateMap[filterId] = null;
      queryCenter.setQuery(filterId, {
        columnId: 100000010,
        key: 't_date',
        dataType: 0,
        dateFilterRelativeUnit: defaultDateItem.timeType, // 默认是自定义粒度
        fieldValue: [
          dayjs(defaultDateItem.value[0]).startOf('day').valueOf(),
          dayjs(defaultDateItem.value[1]).endOf('day').valueOf(),
        ],
        fieldLabel: [
          dayjs(defaultDateItem.value[0]).startOf('day').format('YYYY-MM-DD') +
            ' ~ ' +
            dayjs(defaultDateItem.value[1]).endOf('day').format('YYYY-MM-DD'),
        ],
      });
    };
    linkageCenter.subscribe('reset', resetFilter);
    return () => {
      linkageCenter.unsubscribe('reset', resetFilter);
    };
  }, [defaultDateItem.value, defaultDateItem.timeType, filterId]);
  const onChange = (value: any, dateFilterRelativeUnit: number) => {
    // 临时储存
    temp_dateMap[filterId] = {
      type: dateFilterRelativeUnit,
      value: [value[0], value[1]],
      key,
    };
    queryCenter.setQuery(filterId, {
      columnId: 100000010,
      key: 't_date',
      dataType: 2,
      fieldValue: value,
      fieldLabel: [
        dayjs(value[0]).format('YYYY-MM-DD') +
          ' ~ ' +
          dayjs(value[1]).format('YYYY-MM-DD'),
      ],
      dateFilterRelativeUnit,
    });
  };
  //支持动态渲染
  return (
    <div style={{ width: '100%' }}>
      <DatePickerFilterView
        key={key}
        handleChange={onChange}
        defaultValue={defaultDateItem}
        disabledType={disabledType}
        maxStep={dateRange - 1}
      />
    </div>
  );
};

DateFilterGlobal.displayName = '默认时间筛选器';
